/**
 * CRA (Claim Risk Assessment) JavaScript Module
 * Handles form interactions, validation, and API communication
 */

var baseModule = "cra";

$(document).ready(function() {
    // Initialize CRA form functionality
    initializeCraForm();

    // Initialize page-specific functionality
    initializePageFunctionality();
});

/**
 * Initialize CRA form functionality
 */
function initializeCraForm() {
    // Initialize exposition dropdowns
    initializeExpositionDropdowns();

    // Initialize request management
    initializeRequestManagement();

    // Initialize form validation
    initializeFormValidation();

    // Initialize save buttons
    initializeSaveButtons();

    // Initialize chat functionality only if chat elements exist
    if ($('.send-chat-message').length > 0 || $('#chat-form').length > 0) {
        initializeChatFunctionality();
    }
}

/**
 * Initialize page-specific functionality
 */
function initializePageFunctionality() {
    // Back button functionality for create page
    $('#go-to-list').click(function() {
        window.location.href = baseUrl + '/cra/index';
    });
}

/**
 * Initialize combined exposition dropdown
 */
function initializeExpositionDropdowns() {
    // Handle combined exposition dropdown change
    $('#exposition').change(function() {
        var selectedValue = $(this).val();

        if (selectedValue) {
            // Split the combined value (format: "Level|Detail")
            var parts = selectedValue.split('|');
            if (parts.length === 2) {
                var level = parts[0];
                var detail = parts[1];

                // Update hidden fields for backend compatibility
                $('#expositionLevel').val(level);
                $('#expositionDetail').val(detail);
            }
        } else {
            // Clear hidden fields if no selection
            $('#expositionLevel').val('');
            $('#expositionDetail').val('');
        }
    });

    // Initialize hidden fields if exposition dropdown has a value on page load
    var initialValue = $('#exposition').val();
    if (initialValue) {
        var parts = initialValue.split('|');
        if (parts.length === 2) {
            $('#expositionLevel').val(parts[0]);
            $('#expositionDetail').val(parts[1]);
        }
    }
}

/**
 * Initialize request management functionality
 */
function initializeRequestManagement() {
    // Handle Add request button
    $(document).on('click', '#add-request-number', function(e) {
        // Prevent any form submission or event propagation
        e.preventDefault();
        e.stopPropagation();

        var requestId = $('#requestId').val();
        var listRequests = getListRequests();

        // Only validate Request ID field - no exposition validation required
        // Check for duplicates first
        if ($.inArray(requestId, listRequests) >= 0) {
            showValidationError('Request No already exists');
            return false;
        }
        // Validate numeric and positive
        else if ($.isNumeric(requestId) && requestId > 0) {
            // Make AJAX call to get request data
            $.ajax({
                type: "GET",
                dataType: 'json',
                url: baseUrl + '/' + baseModule + '/getRequestData/' + requestId,
                success: function(result) {
                    if (result.success) {
                        // Business logic validation - brand consistency (DISABLED)
                        // var brandNames = getBrandnames();
                        // if (brandNames.length > 0 && $.inArray(result.request.brandName, brandNames) == -1) {
                        //     showValidationError('Not the same brand name');
                        // }
                        // DAV notification validation (DISABLED)
                        if (result.request.davNotificationNumber == "" || result.request.davExpiringDate == "") {
                            showValidationError('Request hasn\'t Notification Number');
                        }
                        else {
                            // Build table row with request data
                            var tr = '<tr>';
                            tr += '<td class="request id">' + result.request.id + '</td>';
                            tr += '<td class="request brandName">' + result.request.brandName + '</td>';
                            tr += '<td class="request productName">' + result.request.productName + '</td>';
                            tr += '<td class="request davNotificationNumber">' + result.request.davNotificationNumber + '</td>';
                            tr += '<td class="request davReceivingDate">' + result.request.davReceivingDate + '</td>';
                            tr += '<td class="request davExpiringDate">' + result.request.davExpiringDate + '</td>';
                            tr += '<td class="text-center">';
                            tr += '<button class="btn btn-default no-border btn-sm remove-request"><i class="fa fa-minus"></i></button>';
                            tr += '</td>';
                            tr += '</tr>';

                            // Update DOM
                            $('#list-requests').append(tr);
                            $('#list-requests').closest('.table-responsive').show();
                            $('#list-requests').closest('.table-request-info').show();
                            $('#requestId').val(""); // Clear input

                            // Update request IDs
                            setTimeout(collectRequestIds, 100);
                        }
                    } else {
                        showValidationError(result.message || 'Failed to get request data');
                    }
                },
                error: function(result) {
                    showValidationError(result.responseText || 'Error fetching request data');
                }
            });
        }
        else {
            showValidationError('Required to fill request');
        }

        return false; // Ensure no form submission
    });

    // Handle Enter key in request ID input field
    $(document).on('keypress', '#requestId', function(e) {
        if (e.which === 13) { // Enter key
            e.preventDefault();
            e.stopPropagation();
            $('#add-request-number').trigger('click');
            return false;
        }
    });

    // Handle remove request button
    $(document).on('click', '.remove-request', function() {
        var tBody = $(this).closest('tbody');
        var tr = $(this).closest("tr");
        tr.remove();
        if (tBody.find("tr").size() < 1) {
            $('#list-requests').closest('.table-responsive').hide();
        }
        setTimeout(collectRequestIds, 100);
        return false;
    });

    /**
     * Get existing request IDs to prevent duplicates
     */
    function getListRequests() {
        var listRequests = new Array();
        var listCurrentTrs = $('#list-requests').find("tr");
        for (var i = 0; i <= (listCurrentTrs.size() - 1); i++) {
            var currentTr = $(listCurrentTrs.get(i));
            var firstTd = $(currentTr.find("td").get(0));
            listRequests.push(firstTd.html());
        }
        return listRequests;
    }

    /**
     * Get brand names for consistency validation
     */
    function getBrandnames() {
        var listRequests = new Array();
        var listCurrentTrs = $('#list-requests').find("tr");
        for (var i = 0; i <= (listCurrentTrs.size() - 1); i++) {
            var currentTr = $(listCurrentTrs.get(i));
            var secondTd = $(currentTr.find("td").get(1));
            listRequests.push(secondTd.html());
        }
        return listRequests;
    }

    /**
     * Collect request IDs from the table
     */
    function collectRequestIds() {
        var requestIds = [];
        $('#list-requests tr').each(function() {
            var requestId = $(this).find('.request.id').text().trim();
            if (requestId && requestId !== '') {
                requestIds.push(parseInt(requestId));
            }
        });
        $('#requestIds').val(requestIds.join(','));
    }

    /**
     * Collect current files and create hidden inputs
     */
    function collectCurrentFiles() {
        // Collect current content files
        var currentContentFiles = {};
        $('#cra-form').find('.currentContentFiles tbody tr').each(function() {
            var file_id = $(this).attr('id');
            var file_name = $(this).find('td').first().text();
            if (file_id && file_name) {
                currentContentFiles[file_id] = $.trim(file_name);
            }
        });

        // Remove existing hidden input if it exists
        $('input[name="currentContentFiles"]').remove();

        // Create hidden input for current content files
        var inputCurrentContentFiles = $('<input type="hidden" name="currentContentFiles">').val(JSON.stringify(currentContentFiles));
        $('#cra-form').append(inputCurrentContentFiles);

        // Collect current references files
        var currentReferencesFiles = {};
        $('#cra-form').find('.currentReferencesFiles tbody tr').each(function() {
            var file_id = $(this).attr('id');
            var file_name = $(this).find('td').first().text();
            if (file_id && file_name) {
                currentReferencesFiles[file_id] = $.trim(file_name);
            }
        });

        // Remove existing hidden input if it exists
        $('input[name="currentReferencesFiles"]').remove();

        // Create hidden input for current references files
        var inputCurrentReferencesFiles = $('<input type="hidden" name="currentReferencesFiles">').val(JSON.stringify(currentReferencesFiles));
        $('#cra-form').append(inputCurrentReferencesFiles);

        // Collect current proof documents
        var currentProofDocuments = {};
        $('#cra-form').find('.currentProofDocuments tbody tr').each(function() {
            var file_id = $(this).attr('id');
            var file_name = $(this).find('td').first().text();
            if (file_id && file_name) {
                currentProofDocuments[file_id] = $.trim(file_name);
            }
        });

        // Remove existing hidden input if it exists
        $('input[name="currentProofDocuments"]').remove();

        // Create hidden input for current proof documents
        var inputCurrentProofDocuments = $('<input type="hidden" name="currentProofDocuments">').val(JSON.stringify(currentProofDocuments));
        $('#cra-form').append(inputCurrentProofDocuments);
    }

    /**
     * Collect chat messages and create hidden input
     */
    function collectMessages() {
        var messages = [];
        var chatRows = $('#chat-form').find('.list-chat-messages tr');

        chatRows.each(function() {
            var tmpMessage = {};
            tmpMessage.userId = parseInt($(this).find('td.userId').html());
            tmpMessage.email = $(this).find('td.email').html();
            tmpMessage.fullName = $(this).find('td.fullName').html();
            tmpMessage.createdTime = $(this).find('td.createdTime').html();
            tmpMessage.message = $(this).find('td.chatMessage').html();
            var attachmentFiles = null;
            $(this).find("td.fileName div a").each(function() {
                let v = $(this).find("span").html();
                if (attachmentFiles == null) {
                    attachmentFiles = {[$(this).attr("id")]: v.substring(v.indexOf("&nbsp;"), v.length)};
                } else {
                    attachmentFiles[$(this).attr("id")] = v.substring(v.indexOf("&nbsp;"), v.length);
                }
            });
            if (attachmentFiles != null) {
                tmpMessage.attachmentFiles = attachmentFiles;
            }
            messages.push(tmpMessage);
        });

        var messagesJson = JSON.stringify(messages);

        var inputMessages = $("<input type='hidden' name='messages'/>").val(messagesJson);
        $('#cra-form').append(inputMessages);
    }

    /**
     * Collect claims data and create hidden input (similar to collectMessages pattern)
     */
    function collectClaimsData() {
        // Check if getClaimsDataJson function exists globally (from claims section)
        if (typeof window.getClaimsDataJson === 'function') {
            var claimsJson = window.getClaimsDataJson();

            // Remove existing claims input
            $('input[name="claimsData"]').remove();

            // Create new hidden input with claims data
            var claimsInput = $('<input>').attr({
                type: 'hidden',
                name: 'claimsData',
                value: claimsJson
            });

            $('#cra-form').append(claimsInput);
        }
    }

    // Make functions available globally
    window.collectRequestIds = collectRequestIds;
    window.collectCurrentFiles = collectCurrentFiles;
    window.collectMessages = collectMessages;
    window.collectClaimsData = collectClaimsData;
    window.getListRequests = getListRequests;
    window.getBrandnames = getBrandnames;
}

/**
 * Initialize form validation
 */
function initializeFormValidation() {
    /**
     * Validate all required fields for submit
     */
    function validateFullSubmit() {
        // For submit, exposition field IS required
        var exposition = $('#exposition').val();

        if (!exposition) {
            showValidationError('Please select an Exposition');
            return false;
        }

        // Add additional validation for submit
        var advertisementType = $('#advertisementType').val();
        var timeline = $('#timeline').val();

        if (!advertisementType) {
            showValidationError('Please select an Advertisement Type');
            return false;
        }

        if (!timeline) {
            showValidationError('Please select a Timeline');
            return false;
        }

        // Check if at least one request ID is added
        var requestIds = $('#requestIds').val();
        if (!requestIds || requestIds.trim() === '') {
            showValidationError('Please add at least one Request ID');
            return false;
        }

        return true;
    }

    // Make validation functions available globally
    window.validateFullSubmit = validateFullSubmit;
}

/**
 * Initialize submit button functionality
 */
function initializeSaveButtons() {
    // Generic handler for all submit buttons
    $('button.submit').click(function(e) {
        e.preventDefault();

        var typeAction = $(this).attr('id');

        // Store the button ID globally as backup
        window.lastClickedButtonId = typeAction;

        $('#typeAction').val(typeAction);

        // Skip data collection in button click handler - do it only in form submit handler

        // Validation logic for specific actions
        if (typeAction === 'save-submit') {
            // Full validation required for submit actions
            if (window.validateFullSubmit && window.validateFullSubmit()) {
                $('#cra-form').submit();
            }
        } else {
            // For save (draft) and other actions, submit directly
            $('#cra-form').submit();
        }
    });

    // Handle form submission
    $('form#cra-form').submit(function(e) {
        var actionType = $('#typeAction').val();

        // Safeguard: If typeAction is empty, try to get it from the last clicked button
        if (!actionType || actionType === '') {
            if (window.lastClickedButtonId) {
                actionType = window.lastClickedButtonId;
                $('#typeAction').val(actionType);
            }
        }

        // Collect request IDs, current files, and messages before submission
        if (window.collectRequestIds) {
            window.collectRequestIds();
        }
        if (window.collectCurrentFiles) {
            window.collectCurrentFiles();
        }
        if (window.collectMessages) {
            window.collectMessages();
        }
        if (window.collectClaimsData) {
            window.collectClaimsData();
        }

        // CRITICAL FIX: Force set typeAction value just before submission
        if (window.lastClickedButtonId && (!actionType || actionType === '')) {
            actionType = window.lastClickedButtonId;
        }

        // Remove any existing typeAction inputs to prevent conflicts
        $('input[name="typeAction"]').remove();

        // Create a fresh typeAction input with the correct value
        var typeActionInput = $('<input type="hidden" name="typeAction">').val(actionType || window.lastClickedButtonId || 'save');
        $('#cra-form').append(typeActionInput);

        // Final check: Ensure typeAction is not empty
        var finalTypeAction = typeActionInput.val();
        if (!finalTypeAction || finalTypeAction === '') {
            console.error('CRA: ERROR - typeAction is still empty! Preventing form submission.');
            e.preventDefault();
            return false;
        }

        // Only validate for submit actions, not for save (draft) or other form submissions
        if (actionType === 'save-submit') {
            if (!window.validateFullSubmit || !window.validateFullSubmit()) {
                e.preventDefault();
                return false;
            }
        }
        // For save (draft) and other form submissions (like adding requests), no validation required

        return true;
    });
}

/**
 * Utility functions for CRA module
 */

/**
 * Show validation error message
 */
function showValidationError(message) {
    if (typeof jubiqAlert === 'function') {
        jubiqAlert('danger', message);
    } else {
        alert(message);
    }
}

/**
 * Show success message
 */
function showSuccessMessage(message) {
    // Could be enhanced to show in-page notifications
}

/**
 * Show error message
 */
function showErrorMessage(message) {
    // Could be enhanced to show in-page notifications
    console.error('Error: ' + message);
}

/**
 * Initialize chat functionality
 * Note: This function should only be called when chat elements exist
 */
function initializeChatFunctionality() {
    // Chat elements are guaranteed to exist when this function is called

    // Handle chat message submission
    // This functionality is COMPLETELY INDEPENDENT from exposition fields
    $('.send-chat-message').click(function(e) {
        e.preventDefault(); // Explicitly prevent default form submission
        e.stopPropagation(); // Prevent event bubbling

        var chatMessage = $('#chat-form textarea#chat-message').val();

        // Chat validation is independent of exposition fields
        if ((chatMessage || "").trim() != '') {
            if ($("#newAttachmentFiles").val()) {
                uploadFileAttachment(chatMessage);
            } else {
                writeTempMessage(chatMessage);
            }
            return false;
        }
        else {
            jubiqAlert("danger", "Required to fill chat message");
        }
        return false;
    });
}

/**
 * Upload file attachment for chat message
 */
function uploadFileAttachment(chatMessage) {
    let dataForm = new FormData();
    var fileCount = 0;

    $.each($("#newAttachmentFiles")[0].files, function(i, file) {
        dataForm.append('files[]', file);
        fileCount++;
    });

    $.ajax({
        type: "POST",
        dataType: 'json',
        contentType: false,
        processData: false,
        data: dataForm,
        url: baseUrl + '/cra/uploadFiles',
        success: function(result) {
            if (result.success) {
                writeTempMessage(chatMessage, result.fileInfos);
                return false;
            } else {
                console.error('File upload failed:', result);
                jubiqAlert('danger', result.message || 'File upload failed');
            }
            return false;
        },
        error: function(result) {
            console.error('File upload error:', result);
            jubiqAlert('danger', result.responseText || 'File upload error');
        }
    });
}

/**
 * Write temporary chat message to table
 */
function writeTempMessage(chatMessage, fileInfos = null) {
    // Check if required global variables are available
    if (typeof userId === 'undefined' || typeof email === 'undefined' || typeof fullName === 'undefined') {
        console.error('Required global variables not found:', {userId: typeof userId, email: typeof email, fullName: typeof fullName});
        jubiqAlert('danger', 'Chat functionality error: User information not available');
        return;
    }

    var d = new Date();
    var createdTime = d.getFullYear() + "-" + ("0"+(d.getMonth()+1)).slice(-2) + "-" + ("0" + d.getDate()).slice(-2);
    var strFile = "";

    if (fileInfos != null) {
        strFile = "<div style='display: flex;flex-direction: column;white-space: normal;'>";
        Object.keys(fileInfos).forEach(key => {
            strFile += "<a id='" + key + "' class='a-download-file'>" +
                      "    <span class='limit-width'>" + "-&nbsp;" + fileInfos[key] + "</span>" +
                      "</a>";
        });
        strFile += "</div>";
    }

    var tr = '<tr>';
    tr += '<td class="limit-width message userId" style="display:none">' + userId + '</td>';
    tr += '<td class="limit-width message email" style="display:none">' + email + '</td>';
    tr += '<td class="limit-width message fullName">' + fullName + '</td>';
    tr += '<td class="limit-width message createdTime">' + createdTime + '</td>';
    tr += '<td class="limit-width message chatMessage">' + chatMessage + '</td>';
    tr += '<td class="limit-width message fileName">' + strFile + '</td>';
    tr += '</tr>';

    $("#chat-form").find('.list-chat-messages').append(tr);

    // Clear inputs after successful message addition
    $('#chat-form textarea#chat-message').val("");
    $("#newAttachmentFiles").val('');

    // Bind download functionality to new file links
    $('a.a-download-file').on('click', function(event) {
        var fileId = $(this).attr('id');
        var url = baseUrl + '/file/download/' + fileId;
        window.open(url, '_blank');
        return false;
    });
}

/**
 * Get checkbox actions based on user role (following advertising.js pattern)
 */
function getCheckboxActions(type) {
    switch(groupName) {
        case "SCI Manager":
            switch(type) {
                case "SINGULAR":
                    return ["button-assign", "button-cancel"];
                case "PLURALITY":
                    return ["button-assign", "button-cancel"];
                case "ALL":
                    return ["button-assign", "button-cancel"];
                default:
                    return [];
            }
        default:
            return [];
    }
}

/**
 * Handle table row clicks for navigation
 */
$(document).on('click', '.read-cra', function(e) {
    // Don't trigger if clicking on checkbox
    if ($(e.target).is('input[type="checkbox"]') || $(e.target).closest('.checkbox').length) {
        return;
    }

    var row = $(this).closest('tr');
    var itemId = row.find('input[type="checkbox"]').val();

    if (itemId) {
        // Role-based routing using global groupName variable
        if (['Marketing', 'CPD', 'ACD', 'PPD', 'LUXE'].includes(groupName)) {
            window.location = baseUrl + '/cra/update/' + itemId;
        } else if (['SCI Manager', 'SCI Staff'].includes(groupName)) {
            window.location = baseUrl + '/cra/process/' + itemId;
        } else {
            window.location = baseUrl + '/cra/update/' + itemId; // Default fallback
        }
    }
});

// Add hover effect for clickable rows
$(document).on('mouseenter', '.read-cra', function() {
    $(this).closest('tr').addClass('info');
    $(this).css('cursor', 'pointer');
});

$(document).on('mouseleave', '.read-cra', function() {
    $(this).closest('tr').removeClass('info');
});

// Export functions for global access if needed
window.CRA = {
    initializeCraForm: initializeCraForm,
    showSuccessMessage: showSuccessMessage,
    showErrorMessage: showErrorMessage,
    getCheckboxActions: getCheckboxActions,
    initializeChatFunctionality: initializeChatFunctionality,
    writeTempMessage: writeTempMessage,
    uploadFileAttachment: uploadFileAttachment
};
